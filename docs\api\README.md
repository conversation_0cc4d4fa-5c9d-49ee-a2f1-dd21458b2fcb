# 正式版视频平台 API 文档

## 概述

正式版视频平台提供了完整的RESTful API，支持视频上传、管理、播放、用户认证等功能。本文档将帮助开发者快速集成和使用我们的API。

## 🚀 快速开始

### 1. 获取API访问权限

在开始使用API之前，您需要：

1. 注册开发者账号
2. 创建应用并获取API密钥
3. 配置回调地址和权限范围

### 2. 基础信息

- **API基础URL**: `https://api.example.com`
- **API版本**: `v1`
- **数据格式**: `JSON`
- **字符编码**: `UTF-8`

### 3. 认证方式

API使用JWT（JSON Web Token）进行认证：

```http
Authorization: Bearer <your_access_token>
```

## 📋 认证流程

### 用户登录

```bash
curl -X POST https://api.example.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

**响应示例：**

```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "level": 1
    }
  }
}
```

### 令牌刷新

当访问令牌过期时，使用刷新令牌获取新的访问令牌：

```bash
curl -X POST https://api.example.com/api/v1/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "your_refresh_token"
  }'
```

## 🎥 视频管理

### 上传视频

```bash
curl -X POST https://api.example.com/api/v1/upload/video \
  -H "Authorization: Bearer <access_token>" \
  -F "file=@/path/to/video.mp4" \
  -F "type=video"
```

### 创建视频记录

```bash
curl -X POST https://api.example.com/api/v1/videos \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "我的视频",
    "description": "视频描述",
    "video_type": "short",
    "category_id": 1,
    "video_url": "/uploads/videos/video.mp4",
    "cover_image": "/uploads/covers/cover.jpg",
    "tags": ["标签1", "标签2"]
  }'
```

### 获取视频列表

```bash
curl -X GET "https://api.example.com/api/v1/videos?page=1&limit=10&video_type=short" \
  -H "Authorization: Bearer <access_token>"
```

### 获取视频详情

```bash
curl -X GET https://api.example.com/api/v1/videos/1 \
  -H "Authorization: Bearer <access_token>"
```

## 📊 响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "meta": {
    // 元数据（分页信息等）
    "total": 100,
    "per_page": 10,
    "current_page": 1,
    "last_page": 10
  }
}
```

### 成功响应

- `success`: `true`
- `message`: 操作成功的描述信息
- `data`: 返回的具体数据
- `meta`: 元数据（可选）

### 错误响应

- `success`: `false`
- `message`: 错误描述信息
- `data`: `null`
- `error_code`: 错误代码（可选）

## ⚠️ 错误处理

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

### 常见错误代码

| 错误代码 | 说明 |
|----------|------|
| AUTH_001 | 令牌无效 |
| AUTH_002 | 令牌过期 |
| AUTH_003 | 权限不足 |
| VALID_001 | 参数验证失败 |
| UPLOAD_001 | 文件上传失败 |
| VIDEO_001 | 视频不存在 |
| USER_001 | 用户不存在 |

## 🔒 安全最佳实践

### 1. 令牌安全

- 妥善保管访问令牌，不要在客户端明文存储
- 定期刷新令牌，避免长期使用同一令牌
- 在不安全的网络环境下使用HTTPS

### 2. 请求频率限制

- 遵守API的频率限制规则
- 实现指数退避重试机制
- 合理使用缓存减少API调用

### 3. 数据验证

- 始终验证服务器返回的数据
- 对用户输入进行适当的验证和过滤
- 使用参数化查询防止注入攻击

## 📱 SDK 和工具

### 官方SDK

- **JavaScript/Node.js**: [npm包](https://www.npmjs.com/package/@example/video-api)
- **PHP**: [Composer包](https://packagist.org/packages/example/video-api)
- **Python**: [PyPI包](https://pypi.org/project/example-video-api/)
- **Java**: [Maven仓库](https://mvnrepository.com/artifact/com.example/video-api)

### 开发工具

- **Postman集合**: [下载链接](https://example.com/postman-collection)
- **OpenAPI规范**: [Swagger文档](https://api.example.com/swagger/ui)
- **测试工具**: [API测试工具](https://example.com/api-tester)

## 🔄 版本控制

### 版本策略

- API版本通过URL路径指定：`/api/v1/`
- 向后兼容的更改不会增加版本号
- 破坏性更改会发布新版本

### 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| v1.0 | 2024-01-01 | 初始版本发布 |
| v1.1 | 2024-02-01 | 增加视频转码功能 |
| v1.2 | 2024-03-01 | 优化认证流程 |

## 📞 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **开发者社区**: [论坛链接](https://community.example.com)
- **GitHub仓库**: [代码仓库](https://github.com/example/video-api)

### 支持时间

- **工作日**: 9:00 - 18:00 (UTC+8)
- **紧急问题**: 24/7 支持
- **响应时间**: 4小时内回复

## 📚 更多资源

- [API参考文档](https://docs.example.com/api)
- [开发者指南](https://docs.example.com/guides)
- [最佳实践](https://docs.example.com/best-practices)
- [常见问题](https://docs.example.com/faq)
- [更新日志](https://docs.example.com/changelog)
