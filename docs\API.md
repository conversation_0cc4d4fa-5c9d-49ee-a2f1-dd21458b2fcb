# API 接口文档

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: JWT Token
- **请求格式**: JSON
- **响应格式**: JSON

## 认证

### 用户认证

#### 用户注册
```http
POST /auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "confirm_password": "password123"
}
```

#### 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "refresh_token_here",
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

### 管理员认证

#### 管理员登录
```http
POST /admin/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

## 视频管理

### 获取视频列表
```http
GET /videos?page=1&limit=20&category=&search=
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `category`: 分类筛选
- `search`: 搜索关键词

### 获取视频详情
```http
GET /videos/{id}
```

### 上传视频
```http
POST /videos/upload
Content-Type: multipart/form-data

{
  "video": "video_file",
  "title": "视频标题",
  "description": "视频描述",
  "category_id": 1,
  "tags": "标签1,标签2"
}
```

### 获取我的视频
```http
GET /user/videos
Authorization: Bearer {token}
```

## 视频处理

### 获取处理状态
```http
GET /video-processing/{id}/status
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "video_id": 1,
    "overall_status": "processing",
    "overall_progress": 45,
    "current_step": "正在转码...",
    "steps": [
      {
        "type": "upload",
        "status": "completed",
        "progress": 100,
        "message": "上传完成"
      },
      {
        "type": "transcode",
        "status": "processing",
        "progress": 45,
        "message": "正在转码..."
      }
    ]
  }
}
```

### 批量获取处理状态
```http
GET /video-processing/batch-status?video_ids=1,2,3
Authorization: Bearer {token}
```

## 管理员接口

### 视频管理

#### 获取所有视频
```http
GET /admin/videos?page=1&limit=20&status=&audit_status=
Authorization: Bearer {admin_token}
```

#### 审核视频
```http
PUT /admin/videos/{id}/audit
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "audit_status": "approved",
  "audit_comment": "审核通过"
}
```

### 用户管理

#### 获取用户列表
```http
GET /admin/users?page=1&limit=20
Authorization: Bearer {admin_token}
```

#### 更新用户状态
```http
PUT /admin/users/{id}/status
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "active"
}
```

### 系统设置

#### 获取系统配置
```http
GET /admin/settings
Authorization: Bearer {admin_token}
```

#### 更新系统配置
```http
PUT /admin/settings
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "site_title": "视频平台",
  "ffmpeg_path": "/usr/bin/ffmpeg",
  "video_quality": "720p"
}
```

## 视频加密

### 获取解密密钥
```http
GET /video-encryption/{video_id}/key
Authorization: Bearer {token}
```

### 轮换加密密钥
```http
POST /admin/video-encryption/{video_id}/rotate-key
Authorization: Bearer {admin_token}
```

## 错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error_code": "ERROR_CODE",
  "data": null
}
```

## 请求头

### 必需的请求头
```http
Content-Type: application/json
Authorization: Bearer {token}
```

### 管理员接口额外请求头
```http
X-API-Key: {api_key}
```
