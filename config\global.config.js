/**
 * 🔧 全局配置管理系统
 * 统一管理所有环境的配置，避免重复和硬编码
 */

// 环境类型定义
const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 获取当前环境
const getCurrentEnvironment = () => {
  return process.env.NODE_ENV || process.env.APP_ENV || ENVIRONMENTS.DEVELOPMENT
}

// 服务器配置
const SERVER_CONFIGS = {
  [ENVIRONMENTS.DEVELOPMENT]: {
    ip: 'localhost',
    domain: 'localhost'
  },
  [ENVIRONMENTS.PRODUCTION]: {
    ip: process.env.SERVER_IP || '*************',
    domain: process.env.SERVER_DOMAIN || '*************'
  },
  [ENVIRONMENTS.TEST]: {
    ip: process.env.TEST_SERVER_IP || 'test.example.com',
    domain: process.env.TEST_SERVER_DOMAIN || 'test.example.com'
  }
}

// 端口配置
const PORT_CONFIGS = {
  API: parseInt(process.env.API_PORT) || 3000,
  ADMIN: parseInt(process.env.ADMIN_PORT) || 3001,
  FRONTEND: parseInt(process.env.FRONTEND_PORT) || 3002,
  MYSQL: parseInt(process.env.MYSQL_PORT) || 3306,
  REDIS: parseInt(process.env.REDIS_PORT) || 6379
}

// 获取当前环境的服务器配置
const getServerConfig = () => {
  const env = getCurrentEnvironment()
  return SERVER_CONFIGS[env] || SERVER_CONFIGS[ENVIRONMENTS.DEVELOPMENT]
}

// 生成服务URL
const generateServiceUrls = () => {
  const server = getServerConfig()
  const protocol = getCurrentEnvironment() === ENVIRONMENTS.PRODUCTION ? 'https' : 'http'
  
  return {
    api: `${protocol}://${server.domain}:${PORT_CONFIGS.API}`,
    admin: `${protocol}://${server.domain}:${PORT_CONFIGS.ADMIN}`,
    frontend: `${protocol}://${server.domain}:${PORT_CONFIGS.FRONTEND}`,
    ws: `${protocol === 'https' ? 'wss' : 'ws'}://${server.domain}:${PORT_CONFIGS.API}/ws`
  }
}

// 数据库配置
const getDatabaseConfig = () => ({
  host: process.env.DB_HOST || (getCurrentEnvironment() === ENVIRONMENTS.PRODUCTION ? 'mysql' : 'localhost'),
  port: PORT_CONFIGS.MYSQL,
  database: process.env.MYSQL_DATABASE || 'zhengshiban_prod',
  username: process.env.MYSQL_USER || 'shipin_user',
  password: process.env.MYSQL_PASSWORD || 'ShiPin2024@User#Pass456'
})

// Redis配置
const getRedisConfig = () => ({
  host: process.env.REDIS_HOST || (getCurrentEnvironment() === ENVIRONMENTS.PRODUCTION ? 'redis' : 'localhost'),
  port: PORT_CONFIGS.REDIS,
  password: process.env.REDIS_PASSWORD || ''
})

// API密钥配置
const getApiKeys = () => ({
  admin: process.env.VITE_API_KEY_ADMIN || 'ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+',
  user: process.env.VITE_API_KEY_USER || 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+',
  dev: process.env.VITE_API_KEY_DEV || 'ShiPinDev2024ProductionKey32Bytes!@#$%^&*()_+'
})

// 导出配置
module.exports = {
  // 环境信息
  environment: getCurrentEnvironment(),
  isDevelopment: getCurrentEnvironment() === ENVIRONMENTS.DEVELOPMENT,
  isProduction: getCurrentEnvironment() === ENVIRONMENTS.PRODUCTION,
  isTest: getCurrentEnvironment() === ENVIRONMENTS.TEST,
  
  // 服务器配置
  server: getServerConfig(),
  ports: PORT_CONFIGS,
  urls: generateServiceUrls(),
  
  // 数据库配置
  database: getDatabaseConfig(),
  redis: getRedisConfig(),
  
  // 安全配置
  apiKeys: getApiKeys(),
  
  // 应用配置
  app: {
    name: process.env.APP_NAME || '51吃瓜网',
    version: process.env.APP_VERSION || '2.0.0',
    debug: process.env.APP_DEBUG === 'true' || getCurrentEnvironment() === ENVIRONMENTS.DEVELOPMENT
  },
  
  // 工具函数
  utils: {
    generateServiceUrls,
    getCurrentEnvironment,
    getServerConfig
  }
}
