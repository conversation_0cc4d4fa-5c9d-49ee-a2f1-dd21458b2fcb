# 🚀 构建优化指南

## 📋 概述

本文档介绍如何优化Docker构建过程，特别是FFmpeg的处理方式，以减少构建时间和网络依赖。

## ❌ 问题分析

### 当前问题
- **重复下载**：每次构建都需要重新下载FFmpeg（~100MB）
- **构建缓慢**：网络下载导致构建时间长达5-10分钟
- **网络依赖**：服务器环境可能网络受限，下载失败
- **资源浪费**：重复下载相同的二进制文件

### 影响范围
- 开发环境：频繁重建影响开发效率
- CI/CD流水线：构建时间过长
- 生产部署：部署时间不可控
- 离线环境：无法正常构建

## ✅ 优化方案

### 方案1：预下载FFmpeg二进制文件（推荐）

#### 优势
- ✅ 一次下载，多次使用
- ✅ 构建时间从5-10分钟减少到1-2分钟
- ✅ 支持离线构建
- ✅ 减少网络依赖

#### 使用方法

```bash
# 1. 下载FFmpeg二进制文件
bash scripts/download-ffmpeg.sh

# 2. 使用优化构建
bash scripts/build-optimized.sh --use-optimized

# 3. 或者直接使用docker-compose
docker-compose build api
```

#### 文件结构
```
packages/api/
├── ffmpeg-binaries/          # FFmpeg二进制文件目录
│   ├── ffmpeg               # FFmpeg主程序
│   └── ffprobe              # FFprobe工具
├── Dockerfile               # 标准Dockerfile
├── Dockerfile.optimized     # 优化版Dockerfile
└── .dockerignore           # Docker忽略文件
```

### 方案2：使用构建优化脚本

#### 功能特性
- 🔄 自动检测FFmpeg是否存在
- 📥 按需下载FFmpeg二进制文件
- 🏗️ 支持多种构建模式
- 🔍 自动架构检测（amd64/arm64）

#### 脚本选项

```bash
# 显示帮助
bash scripts/build-optimized.sh --help

# 标准构建
bash scripts/build-optimized.sh

# 优化构建
bash scripts/build-optimized.sh --use-optimized

# 强制重新下载FFmpeg
bash scripts/build-optimized.sh --download-ffmpeg --use-optimized

# 无缓存构建
bash scripts/build-optimized.sh --use-optimized --no-cache
```

## 📊 性能对比

| 构建方式 | 首次构建时间 | 重复构建时间 | 网络依赖 | 离线支持 |
|---------|-------------|-------------|----------|----------|
| 标准构建 | 8-12分钟 | 5-8分钟 | 高 | ❌ |
| 优化构建 | 3-5分钟 | 1-2分钟 | 低 | ✅ |

## 🛠️ 实施步骤

### 开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd zhengshiban

# 2. 下载FFmpeg（首次）
bash scripts/download-ffmpeg.sh

# 3. 优化构建
bash scripts/build-optimized.sh --use-optimized

# 4. 启动服务
docker-compose up -d
```

### 生产环境

```bash
# 1. 部署前准备
bash scripts/download-ffmpeg.sh

# 2. 构建生产镜像
bash scripts/build-optimized.sh --use-optimized --no-cache

# 3. 部署服务
docker-compose -f docker-compose.production.yml up -d
```

### CI/CD流水线

```yaml
# .github/workflows/build.yml
name: Build and Deploy

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Download FFmpeg
        run: bash scripts/download-ffmpeg.sh
        
      - name: Build Docker Image
        run: bash scripts/build-optimized.sh --use-optimized
        
      - name: Deploy
        run: docker-compose up -d
```

## 🔧 技术细节

### FFmpeg下载源
- **主源**：GitHub Releases (BtbN/FFmpeg-Builds)
- **架构支持**：amd64, arm64
- **版本**：最新稳定版
- **格式**：静态链接二进制文件

### Dockerfile优化
```dockerfile
# 优化前：通过apt安装
RUN apt-get install -y ffmpeg

# 优化后：复制预下载的二进制文件
COPY ffmpeg-binaries/ffmpeg /usr/local/bin/ffmpeg
COPY ffmpeg-binaries/ffprobe /usr/local/bin/ffprobe
RUN chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe
```

### 缓存策略
- Docker层缓存：优化Dockerfile层顺序
- 二进制文件缓存：本地存储FFmpeg文件
- 依赖缓存：分离依赖安装和应用构建

## 🚨 注意事项

### 文件管理
- FFmpeg二进制文件较大（~100MB），建议添加到.gitignore
- 定期更新FFmpeg版本以获得最新特性和安全修复
- 不同架构需要对应的二进制文件

### 兼容性
- 确保FFmpeg版本与应用兼容
- 测试不同操作系统的兼容性
- 验证所需的编解码器支持

### 安全考虑
- 从可信源下载FFmpeg
- 验证文件完整性
- 定期更新以修复安全漏洞

## 📝 最佳实践

1. **开发环境**：使用优化构建提高开发效率
2. **测试环境**：验证优化构建的稳定性
3. **生产环境**：使用优化构建确保部署速度
4. **版本管理**：定期更新FFmpeg版本
5. **监控告警**：监控构建时间和成功率

## 🔗 相关文档

- [部署指南](../DEPLOYMENT_GUIDE.md)
- [开发者指南](../DEVELOPER_GUIDE.md)
- [Docker最佳实践](https://docs.docker.com/develop/dev-best-practices/)
