name: 测试流水线

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    name: 后端测试
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: zhengshiban_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置PHP环境
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, redis
        coverage: xdebug

    - name: 验证Composer配置
      run: composer validate --strict
      working-directory: packages/api

    - name: 安装Composer依赖
      run: composer install --prefer-dist --no-progress --no-suggest
      working-directory: packages/api

    - name: 复制环境配置
      run: |
        cp .env.example .env.testing
        sed -i 's/DATABASE_HOSTNAME=.*/DATABASE_HOSTNAME=127.0.0.1/' .env.testing
        sed -i 's/DATABASE_DATABASE=.*/DATABASE_DATABASE=zhengshiban_test/' .env.testing
        sed -i 's/DATABASE_USERNAME=.*/DATABASE_USERNAME=root/' .env.testing
        sed -i 's/DATABASE_PASSWORD=.*/DATABASE_PASSWORD=root/' .env.testing
      working-directory: packages/api

    - name: 等待MySQL就绪
      run: |
        until mysqladmin ping -h 127.0.0.1 -P 3306 -u root -proot --silent; do
          echo 'Waiting for MySQL...'
          sleep 2
        done

    - name: 运行数据库迁移
      run: php think migrate:run
      working-directory: packages/api
      env:
        APP_ENV: testing

    - name: 运行PHPUnit测试
      run: |
        mkdir -p tests/results tests/coverage
        ./vendor/bin/phpunit --configuration phpunit.xml --coverage-clover tests/coverage/clover.xml
      working-directory: packages/api

    - name: 上传测试覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: packages/api/tests/coverage/clover.xml
        flags: backend
        name: backend-coverage

  frontend-tests:
    name: 前端测试
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: 安装依赖
      run: npm ci

    - name: 运行前端单元测试
      run: npm run test:run
      working-directory: packages/frontend

    - name: 运行前端测试覆盖率
      run: npm run test:coverage
      working-directory: packages/frontend

    - name: 上传前端覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: packages/frontend/coverage/clover.xml
        flags: frontend
        name: frontend-coverage

  admin-tests:
    name: 管理后台测试
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: 安装依赖
      run: npm ci

    - name: 运行管理后台测试
      run: npm run test:run
      working-directory: packages/admin

    - name: 运行管理后台覆盖率
      run: npm run test:coverage
      working-directory: packages/admin

  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: zhengshiban_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置PHP环境
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, redis

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: 安装后端依赖
      run: composer install --prefer-dist --no-progress
      working-directory: packages/api

    - name: 安装前端依赖
      run: npm ci

    - name: 构建前端应用
      run: |
        npm run build --workspace=packages/frontend
        npm run build --workspace=packages/admin

    - name: 启动服务
      run: |
        # 启动后端API服务
        cd packages/api && php think run -p 8000 &
        
        # 等待服务启动
        sleep 10
        
        # 检查服务状态
        curl -f http://localhost:8000/api/v1/health || exit 1

    - name: 运行集成测试
      run: ./vendor/bin/phpunit tests/Integration/
      working-directory: packages/api
      env:
        APP_ENV: testing
        API_BASE_URL: http://localhost:8000

  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置PHP环境
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, xml, ctype, iconv, intl
        tools: phpcs, phpstan

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: 安装依赖
      run: |
        composer install --prefer-dist --no-progress --working-dir=packages/api
        npm ci

    - name: PHP代码规范检查
      run: phpcs --standard=PSR12 app/
      working-directory: packages/api

    - name: PHP静态分析
      run: phpstan analyse app/ --level=5
      working-directory: packages/api

    - name: 前端代码检查
      run: |
        npm run lint --workspace=packages/frontend
        npm run lint --workspace=packages/admin

    - name: TypeScript类型检查
      run: |
        npm run type-check --workspace=packages/frontend
        npm run type-check --workspace=packages/admin

  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行Trivy漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 上传扫描结果到GitHub Security
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: PHP安全检查
      run: |
        composer audit
      working-directory: packages/api

    - name: Node.js安全检查
      run: |
        npm audit --audit-level=moderate
        npm audit --workspace=packages/frontend --audit-level=moderate
        npm audit --workspace=packages/admin --audit-level=moderate
