# 🚀 视频平台部署指南

> **版本**: v2.0.0  
> **更新时间**: 2025-07-30  
> **适用环境**: 开发环境、测试环境、生产环境

---

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [开发环境部署](#开发环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [环境配置](#环境配置)
- [数据库配置](#数据库配置)
- [性能优化](#性能优化)
- [监控配置](#监控配置)
- [故障排除](#故障排除)

---

## 💻 系统要求

### 最低配置

| 组件 | 要求 |
|------|------|
| **操作系统** | Linux (Ubuntu 20.04+) / Windows 10+ / macOS 10.15+ |
| **PHP** | >= 8.2 |
| **Node.js** | >= 18.0 |
| **MySQL** | >= 8.0 |
| **Redis** | >= 7.0 |
| **内存** | >= 4GB |
| **存储** | >= 50GB |
| **网络** | >= 100Mbps |

### 推荐配置

| 组件 | 推荐 |
|------|------|
| **CPU** | 4核心+ |
| **内存** | 8GB+ |
| **存储** | SSD 200GB+ |
| **网络** | >= 1Gbps |

---

## 🚀 快速开始

### Docker部署 (推荐)

```bash
# 1. 克隆项目
git clone <repository-url>
cd zhengshiban

# 2. 启动服务
docker-compose up -d

# 3. 初始化数据库
docker exec shipin-api php think migrate:run

# 4. 访问应用
# 前端: http://localhost:3001
# 管理后台: http://localhost:3001/admin
# API: http://localhost:3000
```

### 手动部署

```bash
# 1. 安装后端依赖
cd packages/api
composer install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 3. 初始化数据库
php think migrate:run

# 4. 启动后端服务
php think run -H 0.0.0.0 -p 3000

# 5. 安装前端依赖
cd ../admin
npm install

# 6. 启动前端服务
npm run dev
```

---

## 🐳 Docker部署详解

### 服务架构

```yaml
services:
  shipin-mysql:     # MySQL 8.0 数据库
  shipin-redis:     # Redis 7.0 缓存
  shipin-api:       # PHP 8.2 后端API
  shipin-admin:     # Vue 3 管理后台
  shipin-frontend:  # Vue 3 用户前端
  shipin-nginx:     # Nginx 反向代理
```

### 环境配置

1. **复制环境配置文件**
```bash
cp .env.example .env
```

2. **修改关键配置**
```bash
# 数据库配置
DB_HOST=shipin-mysql
DB_PORT=3306
DB_DATABASE=zhengshiban_dev
DB_USERNAME=root
DB_PASSWORD=root123456

# Redis配置
REDIS_HOST=shipin-redis
REDIS_PORT=6379

# 应用配置
APP_DEBUG=false
APP_URL=http://localhost:3000
```

### 数据库初始化

```bash
# 等待MySQL启动
docker-compose exec shipin-mysql mysqladmin ping -h localhost

# 运行数据库迁移
docker-compose exec shipin-api php think migrate:run

# 导入初始数据 (可选)
docker-compose exec shipin-api php think seed:run
```

---

## 🏭 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 项目部署

```bash
# 创建项目目录
sudo mkdir -p /var/www/shipin
cd /var/www/shipin

# 克隆项目
git clone <repository-url> .

# 设置权限
sudo chown -R www-data:www-data .
sudo chmod -R 755 .

# 配置生产环境
cp .env.production .env
# 编辑生产环境配置

# 启动服务
docker-compose -f docker-compose.production.yml up -d
```

### 3. SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 申请SSL证书
sudo certbot --nginx -d yourdomain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 4. 防火墙配置

```bash
# 启用UFW
sudo ufw enable

# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS

# 检查状态
sudo ufw status
```

---

## ⚙️ 环境配置详解

### PHP配置优化

```ini
; php.ini 优化配置
memory_limit = 512M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 100M
post_max_size = 100M

; OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
```

### MySQL配置优化

```ini
# my.cnf 优化配置
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
max_connections = 500
query_cache_size = 128M
tmp_table_size = 64M
max_heap_table_size = 64M
```

### Redis配置优化

```ini
# redis.conf 优化配置
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

---

## 📊 性能优化

### 1. 缓存策略

```php
// API响应缓存
$cache->set('api_response_' . $key, $data, 3600);

// 数据库查询缓存
$result = $cache->remember('user_' . $id, 1800, function() use ($id) {
    return User::find($id);
});
```

### 2. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_videos_category ON videos(category_id);
CREATE INDEX idx_videos_status ON videos(status);
CREATE INDEX idx_users_email ON users(email);

-- 分区表 (大数据量时)
ALTER TABLE videos PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);
```

### 3. CDN配置

```nginx
# Nginx CDN配置
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
    gzip_static on;
}
```

---

## 📈 监控配置

### 1. 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 设置日志轮转
sudo nano /etc/logrotate.d/shipin
```

### 2. 应用监控

```php
// 性能监控中间件
class PerformanceMonitor {
    public function handle($request, $next) {
        $start = microtime(true);
        $response = $next($request);
        $duration = microtime(true) - $start;
        
        // 记录慢查询
        if ($duration > 1.0) {
            Log::warning('Slow request', [
                'url' => $request->url(),
                'duration' => $duration
            ]);
        }
        
        return $response;
    }
}
```

### 3. 健康检查

```bash
# 创建健康检查脚本
cat > /usr/local/bin/health_check.sh << 'EOF'
#!/bin/bash

# 检查服务状态
docker-compose ps

# 检查API健康
curl -f http://localhost:3000/health || exit 1

# 检查数据库连接
docker-compose exec shipin-mysql mysqladmin ping || exit 1

echo "All services healthy"
EOF

chmod +x /usr/local/bin/health_check.sh
```

---

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查MySQL服务状态
docker-compose logs shipin-mysql

# 检查网络连接
docker-compose exec shipin-api ping shipin-mysql

# 重置数据库密码
docker-compose exec shipin-mysql mysql -u root -p
ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

#### 2. 前端构建失败

```bash
# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 检查Node版本
node --version
npm --version
```

#### 3. 视频上传失败

```bash
# 检查存储权限
sudo chown -R www-data:www-data /var/www/shipin/storage
sudo chmod -R 755 /var/www/shipin/storage

# 检查磁盘空间
df -h

# 检查PHP配置
php -i | grep upload_max_filesize
```

#### 4. 性能问题

```bash
# 检查系统资源
top
free -h
df -h

# 检查慢查询
docker-compose exec shipin-mysql mysql -e "SHOW PROCESSLIST;"

# 优化数据库
docker-compose exec shipin-mysql mysql -e "OPTIMIZE TABLE videos;"
```

### 日志分析

```bash
# 查看应用日志
docker-compose logs -f shipin-api

# 查看错误日志
tail -f /var/log/nginx/error.log

# 查看访问日志
tail -f /var/log/nginx/access.log

# 分析日志
grep "ERROR" /var/log/shipin/app.log | tail -20
```

---

## 📞 技术支持

如遇到部署问题，请按以下步骤排查：

1. 检查系统要求是否满足
2. 查看相关日志文件
3. 参考故障排除章节
4. 联系技术支持团队

---

**最后更新**: 2025-07-30  
**文档版本**: v2.0.0