# 部署指南

## 概述

本文档详细介绍了正式版视频平台的部署流程，包括开发环境、测试环境和生产环境的配置。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   管理后台      │    │   API服务       │
│   (Vue 3)       │    │   (Vue 3)       │    │   (ThinkPHP 8)  │
│   Port: 3002    │    │   Port: 3001    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │                负载均衡器                        │
         │              (Nginx/Apache)                     │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │                   数据层                        │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐ │
         │  │   MySQL     │  │    Redis    │  │  文件存储 │ │
         │  │   数据库    │  │    缓存     │  │   (OSS)  │ │
         │  └─────────────┘  └─────────────┘  └──────────┘ │
         └─────────────────────────────────────────────────┘
```

## 🔧 环境要求

### 基础环境

- **操作系统**: Linux (推荐 Ubuntu 20.04+, CentOS 8+)
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **PHP**: 8.2+
- **Node.js**: 18+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

### PHP扩展要求

```bash
# 必需扩展
php-fpm
php-mysql
php-redis
php-gd
php-curl
php-mbstring
php-xml
php-zip
php-intl
php-bcmath
php-fileinfo

# 可选扩展（用于性能优化）
php-opcache
php-apcu
```

### 系统资源建议

| 环境类型 | CPU | 内存 | 存储 | 带宽 |
|----------|-----|------|------|------|
| 开发环境 | 2核 | 4GB | 50GB | 10Mbps |
| 测试环境 | 4核 | 8GB | 100GB | 50Mbps |
| 生产环境 | 8核+ | 16GB+ | 500GB+ | 100Mbps+ |

## 🐳 Docker部署（推荐）

### 1. 克隆项目

```bash
git clone https://github.com/example/zhengshiban.git
cd zhengshiban
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

**关键配置项：**

```env
# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# 数据库配置
DATABASE_HOSTNAME=mysql
DATABASE_DATABASE=zhengshiban
DATABASE_USERNAME=root
DATABASE_PASSWORD=your_secure_password

# Redis配置
REDIS_HOSTNAME=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_ACCESS_TTL=3600
JWT_REFRESH_TTL=604800

# 文件存储配置
FILESYSTEM_DRIVER=local
# 或使用OSS
# FILESYSTEM_DRIVER=oss
# OSS_ACCESS_KEY_ID=your_access_key
# OSS_ACCESS_KEY_SECRET=your_secret_key
# OSS_BUCKET=your_bucket_name
# OSS_ENDPOINT=your_endpoint
```

### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 初始化数据库

```bash
# 进入API容器
docker-compose exec api bash

# 运行数据库迁移
php think migrate:run

# 创建管理员账户
php think admin:create
```

### 5. 配置Nginx

```nginx
# /etc/nginx/sites-available/zhengshiban
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 前端应用
    location / {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 管理后台
    location /admin {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API接口
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件
    location /uploads {
        alias /var/www/zhengshiban/public/uploads;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;
}
```

## 🖥️ 传统部署

### 1. 安装依赖

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx mysql-server redis-server php8.2-fpm php8.2-mysql php8.2-redis php8.2-gd php8.2-curl php8.2-mbstring php8.2-xml php8.2-zip

# CentOS/RHEL
sudo yum install epel-release
sudo yum install nginx mysql-server redis php82-fpm php82-mysql php82-redis php82-gd php82-curl php82-mbstring php82-xml php82-zip
```

### 2. 配置数据库

```bash
# 启动MySQL
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

```sql
CREATE DATABASE zhengshiban CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'zhengshiban'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON zhengshiban.* TO 'zhengshiban'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 配置Redis

```bash
# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis

# 配置Redis密码
sudo vim /etc/redis/redis.conf
# 取消注释并设置密码
# requirepass your_redis_password

sudo systemctl restart redis
```

### 4. 部署应用

```bash
# 克隆代码
cd /var/www
sudo git clone https://github.com/example/zhengshiban.git
sudo chown -R www-data:www-data zhengshiban
cd zhengshiban

# 安装后端依赖
cd packages/api
composer install --no-dev --optimize-autoloader

# 安装前端依赖
cd ../../
npm ci

# 构建前端应用
npm run build --workspace=packages/frontend
npm run build --workspace=packages/admin

# 设置权限
sudo chown -R www-data:www-data /var/www/zhengshiban
sudo chmod -R 755 /var/www/zhengshiban
sudo chmod -R 777 /var/www/zhengshiban/packages/api/runtime
sudo chmod -R 777 /var/www/zhengshiban/public/uploads
```

### 5. 配置PHP-FPM

```bash
# 编辑PHP-FPM配置
sudo vim /etc/php/8.2/fpm/pool.d/www.conf
```

```ini
[www]
user = www-data
group = www-data
listen = /run/php/php8.2-fpm.sock
listen.owner = www-data
listen.group = www-data
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

```bash
# 重启PHP-FPM
sudo systemctl restart php8.2-fpm
sudo systemctl enable php8.2-fpm
```

## 🔍 监控和日志

### 1. 应用监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 设置系统监控
sudo vim /etc/crontab
```

```cron
# 每5分钟检查系统状态
*/5 * * * * root /usr/local/bin/system-check.sh

# 每天备份数据库
0 2 * * * root /usr/local/bin/backup-database.sh

# 每周清理日志
0 0 * * 0 root /usr/local/bin/cleanup-logs.sh
```

### 2. 日志配置

```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/zhengshiban
```

```
/var/www/zhengshiban/packages/api/runtime/log/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload php8.2-fpm
    endscript
}

/var/log/nginx/zhengshiban*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

## 🚀 性能优化

### 1. PHP优化

```ini
# /etc/php/8.2/fpm/php.ini
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000
upload_max_filesize = 100M
post_max_size = 100M

# OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. MySQL优化

```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_type = 1
query_cache_size = 256M
max_connections = 200
```

### 3. Redis优化

```ini
# /etc/redis/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 安装UFW
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL证书

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口占用：`netstat -tlnp | grep :8000`
   - 查看错误日志：`tail -f /var/log/nginx/error.log`

2. **数据库连接失败**
   - 检查MySQL状态：`systemctl status mysql`
   - 验证连接参数：`mysql -h localhost -u zhengshiban -p`

3. **文件上传失败**
   - 检查目录权限：`ls -la public/uploads`
   - 查看PHP配置：`php -i | grep upload`

4. **前端页面空白**
   - 检查构建文件：`ls -la packages/frontend/dist`
   - 查看浏览器控制台错误

### 日志位置

- **应用日志**: `/var/www/zhengshiban/packages/api/runtime/log/`
- **Nginx日志**: `/var/log/nginx/`
- **PHP-FPM日志**: `/var/log/php8.2-fpm.log`
- **MySQL日志**: `/var/log/mysql/`
- **Redis日志**: `/var/log/redis/`

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 参考故障排除章节
4. 联系技术支持：<EMAIL>
