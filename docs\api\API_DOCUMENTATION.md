# 🚀 视频平台API文档

> **版本**: v2.0.0  
> **更新时间**: 2025-07-26  
> **基础URL**: `http://localhost:3000` (开发环境)

---

## 📋 目录

- [认证机制](#认证机制)
- [响应格式](#响应格式)
- [错误处理](#错误处理)
- [用户认证接口](#用户认证接口)
- [视频管理接口](#视频管理接口)
- [分类管理接口](#分类管理接口)
- [评论管理接口](#评论管理接口)
- [用户管理接口](#用户管理接口)
- [文件上传接口](#文件上传接口)
- [管理员接口](#管理员接口)
- [统计分析接口](#统计分析接口)

---

## 🔐 认证机制

### JWT Token认证

所有需要认证的接口都需要在请求头中携带JWT Token：

```http
Authorization: Bearer <your_jwt_token>
```

### API密钥认证

部分管理员接口需要额外的API密钥：

```http
X-API-Key: <your_api_key>
```

---

## 📊 响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": 1640995200
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "参数错误",
  "errors": {
    "field": ["错误详情"]
  }
}
```

---

## ❌ 错误处理

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

### 业务错误码

| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名或密码错误 |
| 1002 | 用户不存在 |
| 1003 | 用户已被禁用 |
| 2001 | 视频不存在 |
| 2002 | 视频审核中 |
| 3001 | 分类不存在 |
| 4001 | 评论不存在 |
| 5001 | 文件上传失败 |

---

## 👤 用户认证接口

### 用户注册

**POST** `/api/auth/register`

#### 请求参数

```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "John Doe"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "nickname": "John Doe"
  }
}
```

### 用户登录

**POST** `/api/auth/login`

#### 请求参数

```json
{
  "username": "john_doe",
  "password": "password123"
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "username": "john_doe",
      "nickname": "John Doe",
      "avatar": "/uploads/avatars/avatar.jpg"
    },
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

### 刷新Token

**POST** `/api/auth/refresh`

#### 请求参数

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 用户登出

**POST** `/api/auth/logout`

**需要认证**: ✅

---

## 🎥 视频管理接口

### 获取视频列表

**GET** `/api/videos`

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |
| category_id | integer | 否 | 分类ID |
| video_type | string | 否 | 视频类型：short/long |
| keyword | string | 否 | 搜索关键词 |
| sort | string | 否 | 排序：latest/popular/views |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取视频列表成功",
  "data": {
    "videos": [
      {
        "id": 1,
        "title": "精彩短视频",
        "description": "这是一个精彩的短视频",
        "video_type": "short",
        "category_id": 1,
        "category_name": "娱乐",
        "user_id": 1,
        "username": "john_doe",
        "nickname": "John Doe",
        "cover_image": "/uploads/covers/cover.jpg",
        "duration": 30,
        "view_count": 1000,
        "like_count": 100,
        "comment_count": 50,
        "is_vip": false,
        "created_at": "2025-01-01 12:00:00"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}
```

### 获取视频详情

**GET** `/api/videos/{id}`

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| id | integer | 视频ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取视频详情成功",
  "data": {
    "id": 1,
    "title": "精彩短视频",
    "description": "这是一个精彩的短视频详细描述",
    "video_type": "short",
    "category_id": 1,
    "category_name": "娱乐",
    "user_id": 1,
    "username": "john_doe",
    "nickname": "John Doe",
    "user_avatar": "/uploads/avatars/avatar.jpg",
    "cover_image": "/uploads/covers/cover.jpg",
    "file_path": "/uploads/videos/video.mp4",
    "hls_url": "/uploads/hls/video.m3u8",
    "duration": 30,
    "view_count": 1000,
    "like_count": 100,
    "comment_count": 50,
    "collect_count": 25,
    "is_vip": false,
    "status": "published",
    "audit_status": "approved",
    "created_at": "2025-01-01 12:00:00",
    "play_urls": [
      {
        "quality": "720p",
        "url": "/uploads/videos/video_720p.mp4"
      },
      {
        "quality": "1080p",
        "url": "/uploads/videos/video_1080p.mp4"
      }
    ]
  }
}
```

### 创建视频

**POST** `/api/videos`

**需要认证**: ✅

#### 请求参数

```json
{
  "title": "新视频标题",
  "description": "视频描述",
  "video_type": "short",
  "category_id": 1,
  "file_path": "/uploads/videos/new_video.mp4",
  "cover_image": "/uploads/covers/new_cover.jpg",
  "duration": 60,
  "is_vip": false
}
```

### 更新视频

**PUT** `/api/videos/{id}`

**需要认证**: ✅

### 删除视频

**DELETE** `/api/videos/{id}`

**需要认证**: ✅

### 视频点赞

**POST** `/api/videos/{id}/like`

**需要认证**: ✅

### 取消点赞

**POST** `/api/videos/{id}/unlike`

**需要认证**: ✅

### 收藏视频

**POST** `/api/videos/{id}/collect`

**需要认证**: ✅

### 取消收藏

**POST** `/api/videos/{id}/uncollect`

**需要认证**: ✅

### 记录观看

**POST** `/api/videos/{id}/view`

**需要认证**: ✅

### 搜索视频

**GET** `/api/videos/search`

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | integer | 否 | 页码 |
| limit | integer | 否 | 每页数量 |

### 推荐视频

**GET** `/api/videos/recommend`

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | integer | 否 | 推荐数量，默认10 |

---

## 📂 分类管理接口

### 获取分类列表

**GET** `/api/categories`

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| video_type | string | 否 | 视频类型：short/long |
| parent_id | integer | 否 | 父分类ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取分类列表成功",
  "data": [
    {
      "id": 1,
      "name": "娱乐",
      "video_type": "short",
      "parent_id": 0,
      "sort_order": 1,
      "status": 1,
      "video_count": 100
    }
  ]
}
```

### 获取分类详情

**GET** `/api/categories/{id}`

### 创建分类

**POST** `/api/categories`

**需要管理员权限**: ✅

#### 请求参数

```json
{
  "name": "新分类",
  "video_type": "short",
  "parent_id": 0,
  "sort_order": 1
}
```

---

## 💬 评论管理接口

### 获取视频评论

**GET** `/api/videos/{video_id}/comments`

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | integer | 否 | 页码 |
| limit | integer | 否 | 每页数量 |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取评论列表成功",
  "data": {
    "comments": [
      {
        "id": 1,
        "video_id": 1,
        "user_id": 1,
        "username": "john_doe",
        "nickname": "John Doe",
        "user_avatar": "/uploads/avatars/avatar.jpg",
        "content": "很棒的视频！",
        "status": "approved",
        "created_at": "2025-01-01 12:00:00"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

### 发表评论

**POST** `/api/videos/{video_id}/comments`

**需要认证**: ✅

#### 请求参数

```json
{
  "content": "这是我的评论内容"
}
```

### 删除评论

**DELETE** `/api/comments/{id}`

**需要认证**: ✅

---

## 👥 用户管理接口

### 获取用户信息

**GET** `/api/user/profile`

**需要认证**: ✅

### 更新用户信息

**PUT** `/api/user/profile`

**需要认证**: ✅

#### 请求参数

```json
{
  "nickname": "新昵称",
  "avatar": "/uploads/avatars/new_avatar.jpg"
}
```

### 修改密码

**PUT** `/api/user/password`

**需要认证**: ✅

#### 请求参数

```json
{
  "old_password": "旧密码",
  "new_password": "新密码"
}
```

### 获取用户视频

**GET** `/api/user/videos`

**需要认证**: ✅

### 获取用户收藏

**GET** `/api/user/collections`

**需要认证**: ✅

### 获取用户统计

**GET** `/api/user/stats`

**需要认证**: ✅

#### 响应示例

```json
{
  "code": 200,
  "message": "获取用户统计成功",
  "data": {
    "video_count": 10,
    "total_views": 5000,
    "total_likes": 500,
    "favorite_count": 25,
    "follower_count": 100,
    "following_count": 50
  }
}
```

---

## 📤 文件上传接口

### 上传视频

**POST** `/api/upload/video`

**需要认证**: ✅

#### 请求参数 (multipart/form-data)

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | file | 是 | 视频文件 |
| chunk | integer | 否 | 分片序号 |
| chunks | integer | 否 | 总分片数 |

#### 响应示例

```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "file_path": "/uploads/videos/video_123456.mp4",
    "file_size": 10485760,
    "duration": 120,
    "upload_id": "upload_123456"
  }
}
```

### 上传图片

**POST** `/api/upload/image`

**需要认证**: ✅

### 获取上传进度

**GET** `/api/upload/progress/{upload_id}`

**需要认证**: ✅

---

## 🔧 管理员接口

### 管理员登录

**POST** `/api/admin/login`

#### 请求参数

```json
{
  "username": "admin",
  "password": "admin123"
}
```

### 获取仪表盘统计

**GET** `/api/admin/dashboard/stats`

**需要管理员权限**: ✅

#### 响应示例

```json
{
  "code": 200,
  "message": "获取统计数据成功",
  "data": {
    "total_users": 1000,
    "today_new_users": 50,
    "total_videos": 5000,
    "today_new_videos": 100,
    "total_views": 100000,
    "today_views": 5000,
    "pending_audits": 20
  }
}
```

### 用户管理

**GET** `/api/admin/users`

**需要管理员权限**: ✅

### 视频管理

**GET** `/api/admin/videos`

**需要管理员权限**: ✅

### 视频审核

**PUT** `/api/admin/videos/{id}/audit`

**需要管理员权限**: ✅

#### 请求参数

```json
{
  "audit_status": "approved",
  "audit_reason": "审核通过"
}
```

---

## 📊 统计分析接口

### 获取视频统计

**GET** `/api/stats/videos`

**需要管理员权限**: ✅

### 获取用户统计

**GET** `/api/stats/users`

**需要管理员权限**: ✅

### 获取实时统计

**GET** `/api/stats/realtime`

**需要管理员权限**: ✅

---

## 🔄 Webhook接口

### 视频处理完成通知

**POST** `/api/webhooks/video/processed`

#### 请求参数

```json
{
  "video_id": 1,
  "status": "success",
  "file_path": "/uploads/videos/processed_video.mp4",
  "hls_url": "/uploads/hls/video.m3u8",
  "thumbnail": "/uploads/thumbnails/thumb.jpg"
}
```

---

## 📝 接口调用示例

### JavaScript (Fetch)

```javascript
// 用户登录
const login = async (username, password) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password })
  });
  
  const data = await response.json();
  if (data.code === 200) {
    localStorage.setItem('access_token', data.data.access_token);
  }
  return data;
};

// 获取视频列表
const getVideos = async (page = 1, limit = 20) => {
  const token = localStorage.getItem('access_token');
  const response = await fetch(`/api/videos?page=${page}&limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};
```

### cURL

```bash
# 用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"john_doe","password":"password123"}'

# 获取视频列表
curl -X GET "http://localhost:3000/api/videos?page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 上传视频
curl -X POST http://localhost:3000/api/upload/video \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/video.mp4"
```

---

## 📋 更新日志

### v2.0.0 (2025-07-26)
- ✨ 新增性能缓存机制
- ✨ 新增系统监控接口
- ✨ 新增视频推荐算法
- 🔧 优化数据库查询性能
- 🔧 完善错误处理机制
- 📚 完善API文档

### v1.0.0 (2024-12-01)
- 🎉 初始版本发布
- ✨ 基础用户认证功能
- ✨ 视频上传和管理功能
- ✨ 分类和评论功能

---

## 📞 技术支持

- **API文档**: [在线文档地址]
- **技术支持**: <EMAIL>
- **问题反馈**: [GitHub Issues]
- **更新通知**: [订阅邮件列表]

---

*本文档持续更新中，如有疑问请联系技术支持团队。*
