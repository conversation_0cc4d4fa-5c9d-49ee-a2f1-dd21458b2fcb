{"version": 3, "file": "cell.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/cell.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport { get } from 'lodash-unified'\nimport { isFunction, isObject } from '@element-plus/utils'\nimport { ExpandIcon, TableCell } from '../components'\nimport { Alignment } from '../constants'\nimport { placeholderSign } from '../private'\nimport { componentToSlot, enforceUnit, tryCall } from '../utils'\n\nimport type { FunctionalComponent, UnwrapNestedRefs, VNode } from 'vue'\nimport type { TableV2RowCellRenderParam } from '../components'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { UseTableReturn } from '../use-table'\nimport type { TableV2Props } from '../table'\n\ntype CellRendererProps = TableV2RowCellRenderParam &\n  Pick<\n    TableV2Props,\n    'cellProps' | 'expandColumnKey' | 'indentSize' | 'iconSize' | 'rowKey'\n  > &\n  UnwrapNestedRefs<Pick<UseTableReturn, 'expandedRowKeys'>> & {\n    ns: UseNamespaceReturn\n  }\n\nconst CellRenderer: FunctionalComponent<CellRendererProps> = (\n  {\n    // renderer props\n    columns,\n    column,\n    columnIndex,\n    depth,\n    expandIconProps,\n    isScrolling,\n    rowData,\n    rowIndex,\n    // from use-table\n    style,\n    expandedRowKeys,\n    ns,\n    // derived props\n    cellProps: _cellProps,\n    expandColumnKey,\n    indentSize,\n    iconSize,\n    rowKey,\n  },\n  { slots }\n) => {\n  const cellStyle = enforceUnit(style)\n\n  if (column.placeholderSign === placeholderSign) {\n    return <div class={ns.em('row-cell', 'placeholder')} style={cellStyle} />\n  }\n  const { cellRenderer, dataKey, dataGetter } = column\n\n  const cellData = isFunction(dataGetter)\n    ? dataGetter({ columns, column, columnIndex, rowData, rowIndex })\n    : get(rowData, dataKey ?? '')\n\n  const extraCellProps = tryCall(_cellProps, {\n    cellData,\n    columns,\n    column,\n    columnIndex,\n    rowIndex,\n    rowData,\n  })\n\n  const cellProps = {\n    class: ns.e('cell-text'),\n    columns,\n    column,\n    columnIndex,\n    cellData,\n    isScrolling,\n    rowData,\n    rowIndex,\n  }\n  const columnCellRenderer = componentToSlot<typeof cellProps>(cellRenderer)\n  const Cell = columnCellRenderer\n    ? columnCellRenderer(cellProps)\n    : renderSlot(slots, 'default', cellProps, () => [\n        <TableCell {...cellProps}></TableCell>,\n      ])\n\n  const kls = [\n    ns.e('row-cell'),\n    column.class,\n    column.align === Alignment.CENTER && ns.is('align-center'),\n    column.align === Alignment.RIGHT && ns.is('align-right'),\n  ]\n\n  const expandable =\n    rowIndex >= 0 && expandColumnKey && column.key === expandColumnKey\n  const expanded = rowIndex >= 0 && expandedRowKeys.includes(rowData[rowKey])\n\n  let IconOrPlaceholder: VNode | undefined\n  const iconStyle = `margin-inline-start: ${depth * indentSize}px;`\n  if (expandable) {\n    if (isObject(expandIconProps)) {\n      IconOrPlaceholder = (\n        <ExpandIcon\n          {...expandIconProps}\n          class={[ns.e('expand-icon'), ns.is('expanded', expanded)]}\n          size={iconSize}\n          expanded={expanded}\n          style={iconStyle}\n          expandable\n        />\n      )\n    } else {\n      IconOrPlaceholder = (\n        <div\n          style={[\n            iconStyle,\n            `width: ${iconSize}px; height: ${iconSize}px;`,\n          ].join(' ')}\n        />\n      )\n    }\n  }\n\n  return (\n    <div class={kls} style={cellStyle} {...extraCellProps} role=\"cell\">\n      {IconOrPlaceholder}\n      {Cell}\n    </div>\n  )\n}\n\nCellRenderer.inheritAttrs = false\n\nexport default CellRenderer\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "columns", "column", "columnIndex", "depth", "expandIconProps", "isScrolling", "rowData", "rowIndex", "style", "expandedRowKeys", "ns", "cellProps", "indentSize", "<PERSON><PERSON><PERSON>", "placeholderSign", "slots", "cellStyle", "cell<PERSON><PERSON><PERSON>", "dataKey", "dataGetter", "cellData", "tryCall", "class", "componentToSlot", "renderSlot", "_createVNode", "TableCell", "Alignment", "column<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cell", "kls", "expandColumnKey", "ExpandIcon", "_mergeProps", "expanded", "includes", "iconStyle", "isObject", "IconOrPlaceholder"], "mappings": ";;;;;;;;;;;;;;AAuBA,EAAMA,OAAAA;AAEF,EAAA,MAAA;EACAC,WAFF;EAGEC,KAHF;EAIEC,eAJF;EAKEC,WALF;EAMEC,OANF;EAOEC,QAPF;EAQEC,KARF;EASEC,eATF;AAUE,EAAA,EAAA;EACAC,SAXF,EAAA,UAAA;EAYEC,eAZF;EAaEC,UAbF;AAcE,EAAA,QAAA;AACAC,EAAAA,MAAAA;GAfF;EAiBEC,KAjBF;MAAA;AAmBEC,EAAAA,MAAAA,SAAAA,GAAAA,iBAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAnBF,EAqBA,IAAA,MAAA,CAAA,eAAA,KAAAC,wBAAA,EAAA;AAAEC,IAAAA,OAAAA,eAAAA,CAAAA,KAAAA,EAAAA;AAAF,MACG,OAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,aAAA,CAAA;AACH,MAAA,OAAe,EAAA,SAAc;;AAE7B,GAAA;AACE,EAAA,MAAA;AAAA,IAAA;WAA4DC;AAA5D,IAAA,UAAA;AACD,GAAA,GAAA,MAAA,CAAA;;IACK,OAAA;IAAEC,MAAF;IAAgBC,WAAhB;AAAyBC,IAAAA,OAAAA;AAAzB,IAAA,QAAN;GAEMC,CAAAA,GAAAA,iBAAAA,CAAAA,SAAqB,OAAA;QACZ,cAAA,GAAAC,aAAA,CAAA,UAAA,EAAA;IAAWpB,QAAX;IAAmBC,OAAnB;IAAgCI,MAAhC;AAAyCC,IAAAA,WAAAA;IADvC;AAIjB,IAAA,OAAoB;IAClBa,CADyC;QAAA,SAAA,GAAA;IAGzCnB,KAHyC,EAAA,EAAA,CAAA,CAAA,CAAA,WAAA,CAAA;IAIzCC,OAJyC;IAKzCK,MALyC;AAMzCD,IAAAA,WAAAA;AANyC,IAA3C,QAAA;AASA,IAAA;AACEgB,IAAAA,OAAOZ;IACPV,QAFgB;IAGhBC;QAHgB,kBAAA,GAAAsB,qBAAA,CAAA,YAAA,CAAA,CAAA;QAAA,IAAA,GAAA,kBAAA,GAAA,kBAAA,CAAA,SAAA,CAAA,GAAAC,cAAA,CAAA,KAAA,EAAA,SAAA,EAAA,SAAA,EAAA,MAAA,CAAAC,eAAA,CAAAC,eAAA,EAAA,SAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;QAAA,GAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,MAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAA,KAAAC,mBAAA,CAAA,MAAA,IAAA,EAAA,CAAA,EAAA,CAAA,cAAA,CAAA,EAAA,MAAA,CAAA,KAAA,KAAAA,mBAAA,CAAA,KAAA,IAAA,EAAA,CAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA;QAAA,UAAA,GAAA,QAAA,IAAA,CAAA,IAAA,eAAA,IAAA,MAAA,CAAA,GAAA,KAAA,eAAA,CAAA;AAQhBpB,EAAAA,MAAAA,QAAAA,GAAAA,QAAAA,IAAAA,CAAAA,IAAAA,eAAAA,CAAAA,QAAAA,CAAAA,OAAAA,CAAAA,MAAAA,CAAAA,CAAAA,CAAAA;EARgB,IAAlB,iBAAA,CAAA;AAUA,EAAA,MAAMqB,SAAkB,GAAA,CAAA,qBAAkB,EAAA,kBAA1C,CAAA,GAAA,CAAA,CAAA;EACA,IAAMC,UAAyB,EAAA;AAM/B,IAAA,IAAMC,gBACF,gBACF7B,EAAM;AAKR,MAAA,iBACU,GAAAwB,eAASM,CAAjBC,qBAAA,EAAAC,cAA0C,CAAA,eAD5C,EAAA;AAEA,QAAMC,OAAQ,EAAA,CAAA,EAAW,CAAA,CAAA,CAAA,aAASzB,CAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CAAAA,UAAgB0B,EAAAA,QAAS7B,CAAAA,CAAAA;AAE3D,QAAA,MAAA,EAAA,QAAA;AACA,QAAM8B,UAAa,EAAA,QAAA;;AACnB,oBAAgB,EAAA,IAAA;AACd,OAAA,CAAIC,EAAQ,IAAA,CAAA,CAAA;AACVC,KAAAA,MAAAA;AAAiB,MAAA,iBAGA,kBAAN,CAAsB5B,KAAA,EAAM;AAHtB,QAAA,OAAA,EAAA,CAAA,SAAA,EAAA,CAAA,OAAA,EAAA,QAAA,CAAA,YAAA,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AAAA,KAAA;AAAA,GAAA;SAAjBe,eAAA,CAAA,KAAA,EAAAQ,cAAA,CAAA;AAUD,IAAA,OAAM,EAAA,GAAA;WACY,EAAA,SAAA;mBAEN,EAAA;UAFX,EAAA,MAAA;AAQD,GAAA,CAAA,EAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACF,CAAA,CAAA;;AAED,WAAA,YAAA;;;;"}