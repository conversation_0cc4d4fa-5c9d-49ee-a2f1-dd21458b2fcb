# 部署指南

## 开发环境部署

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- Git

### 快速启动

1. **克隆项目**
```bash
git clone https://github.com/your-username/zhengshiban.git
cd zhengshiban
```

2. **配置环境变量**
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置文件
nano .env
```

3. **启动服务**
```bash
# Windows用户
start.bat

# Linux/macOS用户
chmod +x start.sh
./start.sh
```

4. **访问应用**
- 用户前端: http://localhost:3002
- 管理后台: http://localhost:3001
- API接口: http://localhost:3000

## 生产环境部署

### 服务器要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 50GB，推荐 200GB+
- **CPU**: 最低 2核，推荐 4核+
- **网络**: 公网IP，开放 80、443 端口

### 1. 安装 Docker

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装依赖
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加 Docker 仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装 Docker
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io docker-compose-plugin
```

#### CentOS/RHEL
```bash
# 安装依赖
sudo yum install -y yum-utils

# 添加 Docker 仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装 Docker
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 部署应用

```bash
# 克隆项目
git clone https://github.com/your-username/zhengshiban.git
cd zhengshiban

# 配置生产环境变量
cp .env.example .env.production
nano .env.production
```

### 3. 生产环境配置

#### 环境变量配置 (.env.production)
```bash
# 应用环境
APP_ENV=production
APP_DEBUG=false

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=zhengshiban_prod
DB_USER=zhengshiban
DB_PASS=your_secure_password

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# 安全配置
API_KEY=your_very_secure_api_key_32_bytes_long
JWT_SECRET=your_jwt_secret_key_64_bytes_long

# 域名配置
FRONTEND_URL=https://your-domain.com
ADMIN_URL=https://admin.your-domain.com
API_URL=https://api.your-domain.com

# SSL证书路径
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# 文件存储
UPLOAD_PATH=/app/storage/uploads
VIDEO_PATH=/app/storage/videos
```

### 4. 启动生产服务

```bash
# 使用生产环境配置启动
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 5. 配置反向代理 (Nginx)

#### 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置文件 (/etc/nginx/sites-available/zhengshiban)
```nginx
# 用户前端
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    location / {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 管理后台
server {
    listen 443 ssl http2;
    server_name admin.your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# API接口
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 1G;
}
```

#### 启用配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/zhengshiban /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. 配置 SSL 证书

#### 使用 Let's Encrypt (免费)
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d admin.your-domain.com -d api.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. 数据库初始化

```bash
# 进入 API 容器
docker-compose -f docker-compose.prod.yml exec api bash

# 运行数据库迁移
php think migrate:run

# 创建管理员账户
php think admin:create
```

### 8. 监控和日志

#### 配置日志轮转
```bash
# 创建日志轮转配置
sudo nano /etc/logrotate.d/zhengshiban

# 添加以下内容
/var/log/zhengshiban/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        docker-compose -f /path/to/zhengshiban/docker-compose.prod.yml restart api
    endscript
}
```

#### 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控 Docker 容器
docker stats

# 查看容器日志
docker-compose -f docker-compose.prod.yml logs -f --tail=100
```

## 维护和更新

### 备份数据
```bash
# 备份数据库
docker-compose -f docker-compose.prod.yml exec mysql mysqldump -u root -p zhengshiban_prod > backup_$(date +%Y%m%d).sql

# 备份文件
tar -czf files_backup_$(date +%Y%m%d).tar.gz storage/
```

### 更新应用
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose -f docker-compose.prod.yml build

# 重启服务
docker-compose -f docker-compose.prod.yml up -d

# 运行数据库迁移
docker-compose -f docker-compose.prod.yml exec api php think migrate:run
```

### 故障排查

#### 常见问题

1. **容器启动失败**
```bash
# 查看容器日志
docker-compose -f docker-compose.prod.yml logs container_name

# 检查容器状态
docker-compose -f docker-compose.prod.yml ps
```

2. **数据库连接失败**
```bash
# 检查数据库容器
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# 检查网络连接
docker network ls
docker network inspect zhengshiban_default
```

3. **文件上传失败**
```bash
# 检查存储目录权限
ls -la storage/
sudo chown -R www-data:www-data storage/
sudo chmod -R 755 storage/
```

## 性能优化

### 数据库优化
- 配置 MySQL 缓存
- 添加适当的索引
- 定期清理日志表

### 缓存优化
- 配置 Redis 持久化
- 设置合适的缓存过期时间
- 使用 CDN 加速静态资源

### 服务器优化
- 配置 Nginx 缓存
- 启用 Gzip 压缩
- 优化内核参数
