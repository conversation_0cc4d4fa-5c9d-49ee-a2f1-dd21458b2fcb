/**
 * 🔧 Vite通用配置
 * 前端和管理后台共享的Vite配置
 */

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 创建通用的Vite配置
export const createViteConfig = (options = {}) => {
  const {
    port = 3002,
    hmrPort = 3002,
    proxyTarget = 'http://shipin-api',
    alias = {},
    plugins = []
  } = options

  return defineConfig(({ command, mode }) => {
    // 加载环境变量
    const env = loadEnv(mode, process.cwd(), '')
    
    const isDev = command === 'serve'
    const isProd = mode === 'production'
    
    // 动态获取HMR主机地址
    const getHmrHost = () => {
      if (env.VITE_HMR_HOST) return env.VITE_HMR_HOST
      if (env.SERVER_IP) return env.SERVER_IP
      return '0.0.0.0'
    }
    
    // 动态获取代理目标
    const getProxyTarget = () => {
      if (env.VITE_API_BASE_URL) return env.VITE_API_BASE_URL
      if (env.VITE_PROXY_TARGET) return env.VITE_PROXY_TARGET
      return proxyTarget
    }

    return {
      plugins: [vue(), ...plugins],
      
      resolve: {
        alias: {
          '@': resolve(process.cwd(), 'src'),
          ...alias
        }
      },
      
      base: '/',
      publicDir: 'public',
      
      server: {
        host: '0.0.0.0',
        port: parseInt(env.VITE_DEV_SERVER_PORT) || port,
        strictPort: true,
        
        hmr: {
          port: parseInt(env.VITE_HMR_PORT) || hmrPort,
          host: getHmrHost()
        },
        
        fs: {
          strict: false
        },
        
        middlewareMode: false,
        open: false,
        
        proxy: {
          '/api': {
            target: getProxyTarget(),
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '/api')
          }
        }
      },
      
      preview: {
        host: '0.0.0.0',
        port: parseInt(env.VITE_PREVIEW_PORT) || port
      },
      
      build: {
        outDir: 'dist',
        sourcemap: isDev || env.VITE_SOURCE_MAP === 'true',
        minify: isProd ? 'terser' : false,
        target: 'es2015',
        cssTarget: 'chrome80',
        
        rollupOptions: {
          output: {
            // 优化的代码分割策略
            manualChunks: (id) => {
              // 第三方库分割
              if (id.includes('node_modules')) {
                // Vue生态系统
                if (id.includes('vue') || id.includes('pinia') || id.includes('@vue')) {
                  return 'vue-vendor'
                }
                // UI库
                if (id.includes('element-plus')) {
                  return 'ui-vendor'
                }
                // 工具库
                if (id.includes('axios') || id.includes('lodash') || id.includes('dayjs')) {
                  return 'utils-vendor'
                }
                // 其他第三方库
                return 'vendor'
              }
              // 按功能模块分割
              if (id.includes('/views/')) {
                const match = id.match(/\/views\/([^\/]+)\//)
                if (match) {
                  return `page-${match[1]}`
                }
              }
            },
            
            // 文件命名策略
            chunkFileNames: (chunkInfo) => {
              const facadeModuleId = chunkInfo.facadeModuleId
              if (facadeModuleId) {
                if (facadeModuleId.includes('/views/')) {
                  return 'js/pages/[name]-[hash].js'
                }
                if (facadeModuleId.includes('/components/')) {
                  return 'js/components/[name]-[hash].js'
                }
              }
              return 'js/[name]-[hash].js'
            },
            
            entryFileNames: 'js/[name]-[hash].js',
            
            assetFileNames: (assetInfo) => {
              if (!assetInfo.name) return 'assets/[name]-[hash].[ext]'
              const info = assetInfo.name.split('.')
              const ext = info[info.length - 1]
              
              if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
                return `media/[name]-[hash].${ext}`
              }
              if (/\.(png|jpe?g|gif|svg|webp|avif)(\?.*)?$/i.test(assetInfo.name)) {
                return `images/[name]-[hash].${ext}`
              }
              if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
                return `fonts/[name]-[hash].${ext}`
              }
              return `assets/[name]-[hash].${ext}`
            }
          }
        },
        
        // 生产环境优化
        ...(isProd && {
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ['console.log', 'console.info', 'console.debug']
            },
            mangle: {
              safari10: true
            }
          },
          cssCodeSplit: true,
          manifest: true,
          chunkSizeWarningLimit: 1000
        })
      },
      
      define: {
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_DEVTOOLS__: isDev || env.VITE_ENABLE_DEVTOOLS === 'true'
      },
      
      envPrefix: 'VITE_'
    }
  })
}

// 前端专用配置
export const createFrontendConfig = () => {
  return createViteConfig({
    port: 3002,
    hmrPort: 3002,
    proxyTarget: 'http://shipin-api'
  })
}

// 管理后台专用配置
export const createAdminConfig = (plugins = []) => {
  return createViteConfig({
    port: 3001,
    hmrPort: 3001,
    proxyTarget: 'http://shipin-api',
    plugins
  })
}
